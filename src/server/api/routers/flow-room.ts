import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '@/server/api/trpc'
import { flowRedisManager } from '@/lib/redis'
import { TRPCError } from '@trpc/server'

const flowDataSchema = z.object({
  nodes: z.array(z.any()),
  edges: z.array(z.any()),
  viewport: z.object({
    x: z.number(),
    y: z.number(),
    zoom: z.number(),
  }).optional(),
})

export const flowRoomRouter = createTRPCRouter({
  // Create a new flow room
  create: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(100),
      description: z.string().optional(),
      isPublic: z.boolean().default(false),
      initialFlowData: flowDataSchema.optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const room = await ctx.db.flowRoom.create({
        data: {
          name: input.name,
          description: input.description,
          isPublic: input.isPublic,
          ownerId: ctx.session.user.id,
          flowData: input.initialFlowData || { nodes: [], edges: [] },
        },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
        },
      })

      // Cache the room data in Redis
      await flowRedisManager.cacheFlowRoom(room.id, {
        roomId: room.id,
        ownerId: room.ownerId,
        flowData: room.flowData,
        lastSyncedAt: new Date().toISOString(),
      })

      return room
    }),

  // Get room by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const room = await ctx.db.flowRoom.findUnique({
        where: { id: input.id },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
        },
      })

      if (!room) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Flow room not found',
        })
      }

      // Check if user has access to this room
      const userId = ctx.session?.user?.id
      const hasAccess = room.isPublic ||
        room.ownerId === userId ||
        room.participants.some(p => p.userId === userId)

      if (!hasAccess) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have access to this room',
        })
      }

      return room
    }),

  // Get user's rooms
  getUserRooms: protectedProcedure
    .query(async ({ ctx }) => {
      const rooms = await ctx.db.flowRoom.findMany({
        where: {
          OR: [
            { ownerId: ctx.session.user.id },
            { participants: { some: { userId: ctx.session.user.id } } },
          ],
        },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      })

      return rooms
    }),

  // Update room flow data
  updateFlowData: protectedProcedure
    .input(z.object({
      roomId: z.string(),
      flowData: flowDataSchema,
    }))
    .mutation(async ({ ctx, input }) => {
      // Check if user has edit access
      const room = await ctx.db.flowRoom.findUnique({
        where: { id: input.roomId },
        include: {
          participants: {
            where: { userId: ctx.session.user.id },
          },
        },
      })

      if (!room) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Flow room not found',
        })
      }

      const isOwner = room.ownerId === ctx.session.user.id
      const isEditor = room.participants.some(p =>
        p.userId === ctx.session.user.id && p.role === 'EDITOR'
      )

      if (!isOwner && !isEditor) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have edit access to this room',
        })
      }

      // Update database
      const updatedRoom = await ctx.db.flowRoom.update({
        where: { id: input.roomId },
        data: {
          flowData: input.flowData,
          updatedAt: new Date(),
        },
      })

      // Update Redis cache
      await flowRedisManager.cacheFlowRoom(input.roomId, {
        roomId: input.roomId,
        ownerId: room.ownerId,
        flowData: input.flowData,
        lastSyncedAt: new Date().toISOString(),
      })

      return updatedRoom
    }),

  // Add participant to room
  addParticipant: protectedProcedure
    .input(z.object({
      roomId: z.string(),
      userId: z.string(),
      role: z.enum(['EDITOR', 'VIEWER']).default('VIEWER'),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check if current user is owner
      const room = await ctx.db.flowRoom.findUnique({
        where: { id: input.roomId },
      })

      if (!room || room.ownerId !== ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only room owners can add participants',
        })
      }

      // Add participant
      const participant = await ctx.db.flowRoomParticipant.create({
        data: {
          roomId: input.roomId,
          userId: input.userId,
          role: input.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      })

      return participant
    }),

  // Remove participant from room
  removeParticipant: protectedProcedure
    .input(z.object({
      roomId: z.string(),
      userId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check if current user is owner or removing themselves
      const room = await ctx.db.flowRoom.findUnique({
        where: { id: input.roomId },
      })

      if (!room) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Flow room not found',
        })
      }

      const isOwner = room.ownerId === ctx.session.user.id
      const isRemovingSelf = input.userId === ctx.session.user.id

      if (!isOwner && !isRemovingSelf) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only remove yourself or be the room owner',
        })
      }

      // Remove participant
      await ctx.db.flowRoomParticipant.delete({
        where: {
          roomId_userId: {
            roomId: input.roomId,
            userId: input.userId,
          },
        },
      })

      return { success: true }
    }),

  // Delete room
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if user is owner
      const room = await ctx.db.flowRoom.findUnique({
        where: { id: input.id },
      })

      if (!room || room.ownerId !== ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only room owners can delete rooms',
        })
      }

      // Delete from database (participants will be cascade deleted)
      await ctx.db.flowRoom.delete({
        where: { id: input.id },
      })

      // Clean up Redis cache
      await flowRedisManager.cleanupRoom(input.id)

      return { success: true }
    }),
})