DATABASE_URL="***********************************************************/postgres?pgbouncer=true&connect_timeout=30&pool_timeout=30"

DIRECT_URL="***********************************************************/postgres?connect_timeout=30"

# Next Auth
NEXTAUTH_SECRET="your-secret-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth (optional - get from https://discord.com/developers/applications)
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

# Google OAuth (optional - get from https://console.cloud.google.com)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Redis configuration for real-time collaboration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# WebSocket URL for client-side connections
NEXT_PUBLIC_WS_URL="http://localhost:3000"

# Skip env validation during build (development only)
SKIP_ENV_VALIDATION=true
